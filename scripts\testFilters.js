import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Prueba los filtros de categorías después de la migración
 */
async function testFilters() {
  console.log('🧪 PROBANDO FILTROS DE CATEGORÍAS');
  console.log('==================================\n');
  
  try {
    // Test 1: Filtro por categoría principal EPP
    console.log('📋 TEST 1: Filtro por categoría EPP');
    const { data: eppProducts, error: eppError } = await supabase
      .from('products')
      .select('name, categories, tags')
      .contains('categories', ['epp'])
      .limit(5);
    
    if (eppError) throw eppError;
    
    console.log(`   ✅ Productos EPP encontrados: ${eppProducts.length}`);
    eppProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Categorías: ${JSON.stringify(product.categories)}`);
    });
    
    // Test 2: Filtro por subcategoría específica - Indumentaria
    console.log('\n📋 TEST 2: Filtro por subcategoría Indumentaria');
    const { data: indumentariaProducts, error: indumentariaError } = await supabase
      .from('products')
      .select('name, categories, tags')
      .contains('categories', ['indumentaria'])
      .limit(5);
    
    if (indumentariaError) throw indumentariaError;
    
    console.log(`   ✅ Productos de indumentaria: ${indumentariaProducts.length}`);
    indumentariaProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Categorías: ${JSON.stringify(product.categories)}`);
    });
    
    // Test 3: Filtro por subcategoría específica - Protección Craneana
    console.log('\n📋 TEST 3: Filtro por subcategoría Protección Craneana');
    const { data: cranealProducts, error: cranealError } = await supabase
      .from('products')
      .select('name, categories, tags')
      .contains('categories', ['craneana'])
      .limit(5);
    
    if (cranealError) throw cranealError;
    
    console.log(`   ✅ Productos de protección craneana: ${cranealProducts.length}`);
    cranealProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Categorías: ${JSON.stringify(product.categories)}`);
    });
    
    // Test 4: Filtro por tags
    console.log('\n📋 TEST 4: Filtro por tags');
    const { data: taggedProducts, error: tagError } = await supabase
      .from('products')
      .select('name, tags')
      .contains('tags', ['seguridad'])
      .limit(5);
    
    if (tagError) throw tagError;
    
    console.log(`   ✅ Productos con tag 'seguridad': ${taggedProducts.length}`);
    taggedProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Tags: ${JSON.stringify(product.tags)}`);
    });
    
    // Test 5: Filtro combinado (categoría + tag)
    console.log('\n📋 TEST 5: Filtro combinado (EPP + tag ropa)');
    const { data: combinedProducts, error: combinedError } = await supabase
      .from('products')
      .select('name, categories, tags')
      .contains('categories', ['epp'])
      .contains('tags', ['ropa'])
      .limit(5);
    
    if (combinedError) throw combinedError;
    
    console.log(`   ✅ Productos EPP con tag 'ropa': ${combinedProducts.length}`);
    combinedProducts.forEach((product, index) => {
      console.log(`   ${index + 1}. ${product.name}`);
      console.log(`      Categorías: ${JSON.stringify(product.categories)}`);
      console.log(`      Tags: ${JSON.stringify(product.tags)}`);
    });
    
    // Test 6: Estadísticas generales
    console.log('\n📋 TEST 6: Estadísticas generales');
    
    // Contar productos por categoría principal
    const { data: allProducts } = await supabase
      .from('products')
      .select('categories, tags');
    
    if (allProducts) {
      const categoryStats = {};
      const tagStats = {};
      
      allProducts.forEach(product => {
        // Estadísticas de categorías
        if (product.categories && product.categories.length > 0) {
          const mainCategory = product.categories[1] || product.categories[0]; // Segunda categoría o primera
          categoryStats[mainCategory] = (categoryStats[mainCategory] || 0) + 1;
        }
        
        // Estadísticas de tags
        if (product.tags && Array.isArray(product.tags)) {
          product.tags.forEach(tag => {
            tagStats[tag] = (tagStats[tag] || 0) + 1;
          });
        }
      });
      
      console.log('\n   📊 Distribución por categorías principales:');
      Object.entries(categoryStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .forEach(([category, count]) => {
          console.log(`      ${category}: ${count} productos`);
        });
      
      console.log('\n   🏷️  Tags más comunes:');
      Object.entries(tagStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .forEach(([tag, count]) => {
          console.log(`      ${tag}: ${count} productos`);
        });
    }
    
    // Test 7: Probar función de búsqueda avanzada (si existe)
    console.log('\n📋 TEST 7: Función de búsqueda avanzada');
    try {
      const { data: searchResults, error: searchError } = await supabase
        .rpc('search_products_advanced', {
          search_term: '',
          category_filters: ['epp'],
          industry_filters: [],
          tag_filters: [],
          property_filters: {}
        });
      
      if (searchError) throw searchError;
      
      console.log(`   ✅ Búsqueda avanzada funcional: ${searchResults.length} resultados`);
      console.log(`   📝 Primeros 3 resultados:`);
      searchResults.slice(0, 3).forEach((product, index) => {
        console.log(`      ${index + 1}. ${product.name}`);
      });
    } catch (error) {
      console.log(`   ⚠️  Búsqueda avanzada no disponible: ${error.message}`);
    }
    
    console.log('\n🎉 TODOS LOS TESTS COMPLETADOS');
    console.log('💡 Los filtros están funcionando correctamente');
    
  } catch (error) {
    console.error('❌ Error durante las pruebas:', error);
    throw error;
  }
}

/**
 * Función para probar filtros específicos del frontend
 */
async function testFrontendFilters() {
  console.log('\n🔍 PROBANDO FILTROS ESPECÍFICOS DEL FRONTEND');
  console.log('=============================================\n');
  
  try {
    // Simular filtros como los usa el frontend
    console.log('📋 Simulando filtros del catálogo...');
    
    // Filtro por múltiples categorías (como en IndustryFilter)
    const { data: multiCategoryProducts, error: multiError } = await supabase
      .from('products')
      .select('name, categories, industries, tags')
      .or('categories.cs.{epp,indumentaria},categories.cs.{epp,craneana}')
      .limit(10);
    
    if (multiError) throw multiError;
    
    console.log(`   ✅ Productos con filtros múltiples: ${multiCategoryProducts.length}`);
    
    // Agrupar por tipo de categoría
    const categoryGroups = {};
    multiCategoryProducts.forEach(product => {
      const secondCategory = product.categories[1] || 'general';
      if (!categoryGroups[secondCategory]) {
        categoryGroups[secondCategory] = [];
      }
      categoryGroups[secondCategory].push(product.name);
    });
    
    Object.entries(categoryGroups).forEach(([category, products]) => {
      console.log(`   📂 ${category}: ${products.length} productos`);
      products.slice(0, 2).forEach(name => {
        console.log(`      - ${name}`);
      });
    });
    
    console.log('\n✅ Filtros del frontend simulados exitosamente');
    
  } catch (error) {
    console.error('❌ Error probando filtros del frontend:', error);
  }
}

// Ejecutar todas las pruebas
testFilters()
  .then(() => testFrontendFilters())
  .then(() => {
    console.log('\n🎉 ¡TODAS LAS PRUEBAS COMPLETADAS EXITOSAMENTE!');
    console.log('💡 Los filtros de categorías están listos para usar');
    console.log('🔗 Puedes probar los filtros en la aplicación web');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Error en las pruebas:', error);
    process.exit(1);
  });
