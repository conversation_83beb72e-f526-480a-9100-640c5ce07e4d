// Google Analytics 4 y GTM Configuration
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Configuración de GA4
export const GA_MEASUREMENT_ID = 'G-35Q226FSWQ'; // ID real de Google Analytics
export const GTM_ID = 'GTM-KL2TLTGL'; // ID real de Google Tag Manager

// Estado de inicialización
let isGAInitialized = false;

// Configurar consentimiento por defecto (antes de cargar GA)
export const setDefaultConsent = () => {
  window.dataLayer = window.dataLayer || [];
  window.gtag = window.gtag || function() {
    window.dataLayer.push(arguments);
  };

  // Configurar consentimiento por defecto como denegado
  window.gtag('consent', 'default', {
    'analytics_storage': 'denied',
    'ad_storage': 'denied',
    'ad_user_data': 'denied',
    'ad_personalization': 'denied',
    'wait_for_update': 500
  });
};

// Actualizar consentimiento basado en la elección del usuario
export const updateConsent = (consent: {
  analytics: boolean;
  marketing: boolean;
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('consent', 'update', {
      'analytics_storage': consent.analytics ? 'granted' : 'denied',
      'ad_storage': consent.marketing ? 'granted' : 'denied',
      'ad_user_data': consent.marketing ? 'granted' : 'denied',
      'ad_personalization': consent.marketing ? 'granted' : 'denied'
    });
  }
};

// Inicializar Google Analytics (GTM ya está cargado en HTML)
export const initGA = (hasAnalyticsConsent: boolean = false) => {
  if (isGAInitialized) return;

  // Configurar consentimiento por defecto primero
  setDefaultConsent();

  // GTM ya está cargado en el HTML, solo configuramos dataLayer
  window.dataLayer = window.dataLayer || [];

  // Configurar gtag si no existe
  window.gtag = window.gtag || function() {
    window.dataLayer.push(arguments);
  };

  // Configurar Google Analytics 4 a través de GTM
  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
    content_group1: 'EPP_Seguridad_Industrial',
    custom_map: {
      'custom_parameter_1': 'industry_type',
      'custom_parameter_2': 'product_category'
    },
    // Configuración de privacidad
    anonymize_ip: true,
    allow_google_signals: hasAnalyticsConsent,
    cookie_expires: 365 * 24 * 60 * 60 // 1 año en segundos
  });

  isGAInitialized = true;
};

// Verificar si se puede hacer tracking
const canTrack = (): boolean => {
  // Verificar si hay consentimiento para analytics
  const consent = localStorage.getItem('cr-work-cookie-consent');
  if (consent) {
    try {
      const parsed = JSON.parse(consent);
      return parsed.consent?.analytics === true;
    } catch {
      return false;
    }
  }
  return false;
};

// Eventos de seguimiento específicos para SEO y conversión
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && window.gtag && canTrack()) {
    window.gtag('event', eventName, parameters);
  }
};

// Enviar eventos específicos a GTM
export const trackGTMEvent = (eventName: string, eventData: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && window.dataLayer && canTrack()) {
    window.dataLayer.push({
      event: eventName,
      ...eventData
    });
  }
};

// Evento: Click en CTA principal
export const trackCTAClick = (ctaName: string, location: string) => {
  trackEvent('cta_click', {
    event_category: 'engagement',
    event_label: ctaName,
    cta_location: location,
    value: 1
  });

  // También enviar a GTM para seguimiento avanzado
  trackGTMEvent('cta_click', {
    cta_name: ctaName,
    cta_location: location,
    page_title: document.title,
    page_url: window.location.href
  });
};

// Evento: Click en enlaces internos
export const trackInternalLink = (linkText: string, destination: string, section: string) => {
  trackEvent('internal_link_click', {
    event_category: 'navigation',
    event_label: linkText,
    link_destination: destination,
    page_section: section
  });
};

// Evento: Scroll profundo (≥75%)
export const trackDeepScroll = (percentage: number) => {
  trackEvent('scroll_depth', {
    event_category: 'engagement',
    event_label: `${percentage}%`,
    value: percentage
  });
};

// Evento: Visualización de producto
export const trackProductView = (productName: string, category: string, brand?: string) => {
  trackEvent('view_item', {
    event_category: 'ecommerce',
    item_name: productName,
    item_category: category,
    item_brand: brand || 'Sin marca',
    currency: 'ARS'
  });
};

// Evento: Búsqueda
export const trackSearch = (searchTerm: string, resultsCount: number) => {
  trackEvent('search', {
    event_category: 'site_search',
    search_term: searchTerm,
    results_count: resultsCount
  });
};

// Evento: Contacto/Lead
export const trackLead = (method: string, category: string = 'epp') => {
  trackEvent('generate_lead', {
    event_category: 'conversion',
    contact_method: method,
    product_interest: category,
    value: 10 // Valor estimado del lead
  });
};

// Evento: Descarga de catálogo
export const trackCatalogDownload = (catalogType: string) => {
  trackEvent('file_download', {
    event_category: 'engagement',
    file_name: catalogType,
    file_type: 'PDF'
  });
};

// Configurar scroll tracking automático
export const setupScrollTracking = () => {
  let scrollDepths = [25, 50, 75, 90];
  let trackedDepths: number[] = [];

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = Math.round((scrollTop / docHeight) * 100);

    scrollDepths.forEach(depth => {
      if (scrollPercent >= depth && !trackedDepths.includes(depth)) {
        trackedDepths.push(depth);
        trackDeepScroll(depth);
      }
    });
  };

  window.addEventListener('scroll', handleScroll, { passive: true });
};

// Configurar tracking de enlaces internos automático
export const setupLinkTracking = () => {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    const link = target.closest('a');
    
    if (link && link.hostname === window.location.hostname) {
      const linkText = link.textContent?.trim() || link.getAttribute('aria-label') || 'Sin texto';
      const destination = link.pathname;
      const section = link.closest('section')?.className || 'unknown';
      
      trackInternalLink(linkText, destination, section);
    }
  });
};

// Objetivos de conversión sugeridos para GA4
export const CONVERSION_GOALS = {
  // Micro-conversiones
  CATALOG_DOWNLOAD: 'catalog_download',
  CONTACT_FORM: 'contact_form_submit',
  PHONE_CLICK: 'phone_click',
  WHATSAPP_CLICK: 'whatsapp_click',
  EMAIL_CLICK: 'email_click',
  
  // Macro-conversiones
  QUOTE_REQUEST: 'quote_request',
  PRODUCT_INQUIRY: 'product_inquiry',
  NEWSLETTER_SIGNUP: 'newsletter_signup',
  
  // Engagement
  DEEP_SCROLL: 'scroll_75_percent',
  TIME_ON_SITE: 'engaged_session',
  MULTIPLE_PAGES: 'page_view_3_plus'
};

// Configuración de Enhanced Ecommerce para productos EPP
export const trackEcommerceEvent = (action: string, items: any[]) => {
  trackEvent(action, {
    event_category: 'ecommerce',
    currency: 'ARS',
    items: items
  });

  // Enviar también a GTM para Enhanced Ecommerce
  trackGTMEvent(action, {
    currency: 'ARS',
    items: items,
    ecommerce: {
      currency: 'ARS',
      items: items
    }
  });
};

// Eventos específicos para GTM Enhanced Ecommerce
export const trackProductView = (product: any) => {
  trackGTMEvent('view_item', {
    currency: 'ARS',
    value: product.price || 0,
    items: [{
      item_id: product.id,
      item_name: product.name,
      item_category: product.category,
      item_brand: product.brand,
      price: product.price || 0,
      quantity: 1
    }]
  });
};

export const trackAddToCart = (product: any, quantity: number = 1) => {
  trackGTMEvent('add_to_cart', {
    currency: 'ARS',
    value: (product.price || 0) * quantity,
    items: [{
      item_id: product.id,
      item_name: product.name,
      item_category: product.category,
      item_brand: product.brand,
      price: product.price || 0,
      quantity: quantity
    }]
  });
};

export const trackQuoteRequest = (products: any[]) => {
  const totalValue = products.reduce((sum, product) => sum + (product.price || 0) * (product.quantity || 1), 0);

  trackGTMEvent('generate_lead', {
    currency: 'ARS',
    value: totalValue,
    lead_type: 'quote_request',
    items: products.map(product => ({
      item_id: product.id,
      item_name: product.name,
      item_category: product.category,
      item_brand: product.brand,
      price: product.price || 0,
      quantity: product.quantity || 1
    }))
  });
};
