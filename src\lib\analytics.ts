// Google Analytics 4 y GTM Configuration
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}

// Configuración de GA4
export const GA_MEASUREMENT_ID = 'G-35Q226FSWQ'; // ID real de Google Analytics
export const GTM_ID = 'GTM-XXXXXXX'; // Reemplazar con tu ID real cuando lo tengas

// Inicializar Google Analytics
export const initGA = () => {
  // Google Tag Manager
  window.dataLayer = window.dataLayer || [];
  
  // Cargar GTM script
  const gtmScript = document.createElement('script');
  gtmScript.async = true;
  gtmScript.src = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}`;
  document.head.appendChild(gtmScript);

  // Configurar dataLayer inicial
  window.dataLayer.push({
    'gtm.start': new Date().getTime(),
    event: 'gtm.js'
  });

  // Google Analytics 4
  const gaScript = document.createElement('script');
  gaScript.async = true;
  gaScript.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
  document.head.appendChild(gaScript);

  window.gtag = function() {
    window.dataLayer.push(arguments);
  };

  window.gtag('js', new Date());
  window.gtag('config', GA_MEASUREMENT_ID, {
    page_title: document.title,
    page_location: window.location.href,
    content_group1: 'EPP_Seguridad_Industrial',
    custom_map: {
      'custom_parameter_1': 'industry_type',
      'custom_parameter_2': 'product_category'
    }
  });
};

// Eventos de seguimiento específicos para SEO y conversión
export const trackEvent = (eventName: string, parameters: Record<string, any> = {}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

// Evento: Click en CTA principal
export const trackCTAClick = (ctaName: string, location: string) => {
  trackEvent('cta_click', {
    event_category: 'engagement',
    event_label: ctaName,
    cta_location: location,
    value: 1
  });
};

// Evento: Click en enlaces internos
export const trackInternalLink = (linkText: string, destination: string, section: string) => {
  trackEvent('internal_link_click', {
    event_category: 'navigation',
    event_label: linkText,
    link_destination: destination,
    page_section: section
  });
};

// Evento: Scroll profundo (≥75%)
export const trackDeepScroll = (percentage: number) => {
  trackEvent('scroll_depth', {
    event_category: 'engagement',
    event_label: `${percentage}%`,
    value: percentage
  });
};

// Evento: Visualización de producto
export const trackProductView = (productName: string, category: string, brand?: string) => {
  trackEvent('view_item', {
    event_category: 'ecommerce',
    item_name: productName,
    item_category: category,
    item_brand: brand || 'Sin marca',
    currency: 'ARS'
  });
};

// Evento: Búsqueda
export const trackSearch = (searchTerm: string, resultsCount: number) => {
  trackEvent('search', {
    event_category: 'site_search',
    search_term: searchTerm,
    results_count: resultsCount
  });
};

// Evento: Contacto/Lead
export const trackLead = (method: string, category: string = 'epp') => {
  trackEvent('generate_lead', {
    event_category: 'conversion',
    contact_method: method,
    product_interest: category,
    value: 10 // Valor estimado del lead
  });
};

// Evento: Descarga de catálogo
export const trackCatalogDownload = (catalogType: string) => {
  trackEvent('file_download', {
    event_category: 'engagement',
    file_name: catalogType,
    file_type: 'PDF'
  });
};

// Configurar scroll tracking automático
export const setupScrollTracking = () => {
  let scrollDepths = [25, 50, 75, 90];
  let trackedDepths: number[] = [];

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = Math.round((scrollTop / docHeight) * 100);

    scrollDepths.forEach(depth => {
      if (scrollPercent >= depth && !trackedDepths.includes(depth)) {
        trackedDepths.push(depth);
        trackDeepScroll(depth);
      }
    });
  };

  window.addEventListener('scroll', handleScroll, { passive: true });
};

// Configurar tracking de enlaces internos automático
export const setupLinkTracking = () => {
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement;
    const link = target.closest('a');
    
    if (link && link.hostname === window.location.hostname) {
      const linkText = link.textContent?.trim() || link.getAttribute('aria-label') || 'Sin texto';
      const destination = link.pathname;
      const section = link.closest('section')?.className || 'unknown';
      
      trackInternalLink(linkText, destination, section);
    }
  });
};

// Objetivos de conversión sugeridos para GA4
export const CONVERSION_GOALS = {
  // Micro-conversiones
  CATALOG_DOWNLOAD: 'catalog_download',
  CONTACT_FORM: 'contact_form_submit',
  PHONE_CLICK: 'phone_click',
  WHATSAPP_CLICK: 'whatsapp_click',
  EMAIL_CLICK: 'email_click',
  
  // Macro-conversiones
  QUOTE_REQUEST: 'quote_request',
  PRODUCT_INQUIRY: 'product_inquiry',
  NEWSLETTER_SIGNUP: 'newsletter_signup',
  
  // Engagement
  DEEP_SCROLL: 'scroll_75_percent',
  TIME_ON_SITE: 'engaged_session',
  MULTIPLE_PAGES: 'page_view_3_plus'
};

// Configuración de Enhanced Ecommerce para productos EPP
export const trackEcommerceEvent = (action: string, items: any[]) => {
  trackEvent(action, {
    event_category: 'ecommerce',
    currency: 'ARS',
    items: items
  });
};
