import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { verifyDatabaseStructure } from './verifyDatabaseStructure.js';
import migrateCategoriesAndTags from './migrateCategoriesAndTags.js';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

/**
 * Script principal para configurar los filtros de categorías
 */
async function setupCategoryFilters() {
  console.log('🚀 CONFIGURACIÓN DE FILTROS DE CATEGORÍAS');
  console.log('==========================================\n');
  
  try {
    // Paso 1: Verificar estructura de la base de datos
    console.log('📋 PASO 1: Verificando estructura de la base de datos...');
    const isStructureOk = await verifyDatabaseStructure();
    
    if (!isStructureOk) {
      console.log('\n⚠️  Se detectaron problemas en la estructura de la base de datos');
      console.log('🔧 Procediendo con la migración...\n');
    } else {
      console.log('\n✅ Estructura de la base de datos verificada\n');
    }
    
    // Paso 2: Ejecutar migración de categorías y tags
    console.log('📋 PASO 2: Ejecutando migración de categorías y tags...');
    await migrateCategoriesAndTags();
    console.log('✅ Migración completada\n');
    
    // Paso 3: Verificar que los filtros funcionen
    console.log('📋 PASO 3: Verificando funcionamiento de filtros...');
    await testFilters();
    
    // Paso 4: Generar reporte final
    console.log('📋 PASO 4: Generando reporte final...');
    await generateFinalReport();
    
    console.log('\n🎉 ¡CONFIGURACIÓN COMPLETADA EXITOSAMENTE!');
    console.log('💡 Los filtros de categorías ahora deberían funcionar correctamente');
    console.log('🔗 Puedes probar los filtros en la aplicación web');
    
  } catch (error) {
    console.error('💥 Error durante la configuración:', error);
    throw error;
  }
}

/**
 * Prueba el funcionamiento de los filtros
 */
async function testFilters() {
  console.log('🧪 Probando filtros de categorías...');
  
  try {
    // Test 1: Filtro por categoría EPP
    const { data: eppProducts, error: eppError } = await supabase
      .from('products')
      .select('name, categories')
      .contains('categories', ['epp']);
    
    if (eppError) throw eppError;
    
    console.log(`   ✅ Productos EPP encontrados: ${eppProducts.length}`);
    
    // Test 2: Filtro por subcategoría específica
    const { data: cranealProducts, error: cranealError } = await supabase
      .from('products')
      .select('name, categories')
      .contains('categories', ['craneana']);
    
    if (cranealError) throw cranealError;
    
    console.log(`   ✅ Productos de protección craneana: ${cranealProducts.length}`);
    
    // Test 3: Filtro por tags
    const { data: taggedProducts, error: tagError } = await supabase
      .from('products')
      .select('name, tags')
      .contains('tags', ['seguridad']);
    
    if (tagError) throw tagError;
    
    console.log(`   ✅ Productos con tag 'seguridad': ${taggedProducts.length}`);
    
    // Test 4: Función de búsqueda avanzada
    try {
      const { data: searchResults, error: searchError } = await supabase
        .rpc('search_products_advanced', {
          search_term: '',
          category_filters: ['epp'],
          industry_filters: [],
          tag_filters: [],
          property_filters: {}
        });
      
      if (searchError) throw searchError;
      
      console.log(`   ✅ Búsqueda avanzada funcional: ${searchResults.length} resultados`);
    } catch (error) {
      console.log(`   ⚠️  Búsqueda avanzada no disponible: ${error.message}`);
    }
    
  } catch (error) {
    console.error('❌ Error probando filtros:', error.message);
  }
}

/**
 * Genera un reporte final del estado de la base de datos
 */
async function generateFinalReport() {
  console.log('📊 Generando reporte final...');
  
  try {
    // Estadísticas generales
    const { count: totalProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true });
    
    const { data: categorizedProducts } = await supabase
      .from('products')
      .select('categories')
      .not('categories', 'is', null)
      .neq('categories', '{}');
    
    const { data: taggedProducts } = await supabase
      .from('products')
      .select('tags')
      .not('tags', 'is', null)
      .neq('tags', '{}');
    
    const { data: productsWithProperties } = await supabase
      .from('products')
      .select('properties')
      .not('properties', 'is', null)
      .neq('properties', '{}');
    
    console.log('\n📈 REPORTE FINAL:');
    console.log('================');
    console.log(`📦 Total de productos: ${totalProducts || 0}`);
    console.log(`📂 Productos con categorías: ${categorizedProducts?.length || 0}`);
    console.log(`🏷️  Productos con tags: ${taggedProducts?.length || 0}`);
    console.log(`⚙️  Productos con propiedades: ${productsWithProperties?.length || 0}`);
    
    // Distribución por categorías principales
    if (categorizedProducts && categorizedProducts.length > 0) {
      const categoryDistribution = {};
      categorizedProducts.forEach(product => {
        const mainCategory = product.categories[0] || 'sin-categoria';
        categoryDistribution[mainCategory] = (categoryDistribution[mainCategory] || 0) + 1;
      });
      
      console.log('\n📊 Distribución por categorías principales:');
      Object.entries(categoryDistribution)
        .sort(([,a], [,b]) => b - a)
        .forEach(([category, count]) => {
          console.log(`   ${category}: ${count} productos`);
        });
    }
    
    // Tags más comunes
    if (taggedProducts && taggedProducts.length > 0) {
      const tagDistribution = {};
      taggedProducts.forEach(product => {
        if (product.tags && Array.isArray(product.tags)) {
          product.tags.forEach(tag => {
            tagDistribution[tag] = (tagDistribution[tag] || 0) + 1;
          });
        }
      });
      
      console.log('\n🏷️  Tags más comunes:');
      Object.entries(tagDistribution)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .forEach(([tag, count]) => {
          console.log(`   ${tag}: ${count} productos`);
        });
    }
    
    // Recomendaciones
    console.log('\n💡 RECOMENDACIONES:');
    
    const categorizationRate = categorizedProducts?.length / totalProducts * 100 || 0;
    const taggingRate = taggedProducts?.length / totalProducts * 100 || 0;
    
    if (categorizationRate < 90) {
      console.log(`⚠️  Solo ${categorizationRate.toFixed(1)}% de productos tienen categorías asignadas`);
      console.log('   Considera revisar productos sin categorizar');
    } else {
      console.log(`✅ ${categorizationRate.toFixed(1)}% de productos categorizados correctamente`);
    }
    
    if (taggingRate < 80) {
      console.log(`⚠️  Solo ${taggingRate.toFixed(1)}% de productos tienen tags asignados`);
      console.log('   Los tags mejoran la búsqueda y filtrado');
    } else {
      console.log(`✅ ${taggingRate.toFixed(1)}% de productos con tags asignados`);
    }
    
    console.log('\n🔗 PRÓXIMOS PASOS:');
    console.log('1. Probar los filtros en la aplicación web');
    console.log('2. Verificar que la búsqueda funcione correctamente');
    console.log('3. Considerar agregar más productos de ejemplo si es necesario');
    console.log('4. Revisar y ajustar categorías según feedback de usuarios');
    
  } catch (error) {
    console.error('❌ Error generando reporte:', error.message);
  }
}

// Ejecutar configuración
if (import.meta.url === `file://${process.argv[1]}`) {
  setupCategoryFilters()
    .then(() => {
      console.log('\n✨ ¡Configuración finalizada!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Error fatal:', error);
      process.exit(1);
    });
}

export default setupCategoryFilters;
