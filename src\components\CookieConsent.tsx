import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, X, Shield, BarChart3 } from 'lucide-react';
import { initGA, trackEvent } from '../lib/analytics';

interface CookieConsentProps {
  onConsentChange?: (consent: CookieConsent) => void;
}

interface CookieConsent {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
}

const COOKIE_CONSENT_KEY = 'cr-work-cookie-consent';
const CONSENT_VERSION = '1.0';

const CookieConsent: React.FC<CookieConsentProps> = ({ onConsentChange }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [consent, setConsent] = useState<CookieConsent>({
    essential: true,
    analytics: true,
    marketing: true,
    timestamp: Date.now()
  });

  useEffect(() => {
    // Verificar si ya existe consentimiento
    const savedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (savedConsent) {
      try {
        const parsed = JSON.parse(savedConsent);
        // Verificar si es la versión actual
        if (parsed.version === CONSENT_VERSION) {
          setConsent(parsed.consent);
          // Inicializar analytics si fue aceptado
          if (parsed.consent.analytics) {
            initGA();
          }
          return;
        }
      } catch (error) {
        console.error('Error parsing saved consent:', error);
      }
    }
    
    // Mostrar banner si no hay consentimiento válido
    setIsVisible(true);
  }, []);

  const saveConsent = (newConsent: CookieConsent) => {
    const consentData = {
      version: CONSENT_VERSION,
      consent: newConsent,
      timestamp: Date.now()
    };
    
    localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consentData));
    setConsent(newConsent);
    setIsVisible(false);
    
    // Inicializar analytics si fue aceptado
    if (newConsent.analytics) {
      initGA();
      trackEvent('cookie_consent_analytics_accepted', {
        event_category: 'privacy',
        event_label: 'analytics_cookies'
      });
    }
    
    // Callback para notificar cambios
    onConsentChange?.(newConsent);
    
    trackEvent('cookie_consent_saved', {
      event_category: 'privacy',
      analytics: newConsent.analytics,
      marketing: newConsent.marketing
    });
  };

  const handleAcceptAll = () => {
    const newConsent: CookieConsent = {
      essential: true,
      analytics: true,
      marketing: true,
      timestamp: Date.now()
    };
    saveConsent(newConsent);
  };

  const handleAcceptEssential = () => {
    const newConsent: CookieConsent = {
      essential: true,
      analytics: false,
      marketing: false,
      timestamp: Date.now()
    };
    saveConsent(newConsent);
  };

  const handleCustomSave = () => {
    saveConsent(consent);
  };

  const toggleAnalytics = () => {
    setConsent(prev => ({ ...prev, analytics: !prev.analytics }));
  };

  const toggleMarketing = () => {
    setConsent(prev => ({ ...prev, marketing: !prev.marketing }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[70] p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {!showDetails ? (
            // Vista compacta
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <Cookie className="h-6 w-6 text-primary mt-0.5 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Configuración de Cookies
                  </h3>
                  <p className="text-sm text-gray-700 leading-relaxed">
                    Utilizamos cookies para mejorar tu experiencia, analizar el tráfico del sitio y personalizar el contenido.
                    Puedes elegir qué tipos de cookies aceptar.
                  </p>
                </div>
              </div>

              <div className="flex flex-col gap-3">
                <button
                  onClick={() => setShowDetails(true)}
                  className="text-sm text-primary hover:text-primary-dark underline text-left"
                >
                  Ver configuración detallada
                </button>

                <div className="flex flex-col sm:flex-row gap-2">
                  <button
                    onClick={handleAcceptEssential}
                    className="px-4 py-2 text-sm border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                  >
                    Solo esenciales
                  </button>
                  <button
                    onClick={handleAcceptAll}
                    className="px-4 py-2 text-sm bg-primary text-white hover:bg-primary-dark rounded-md transition-colors"
                  >
                    Aceptar todas
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Vista detallada
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-primary" />
                  Configuración Detallada de Cookies
                </h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            
            <div className="space-y-3">
              {/* Cookies esenciales */}
              <div className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Cookies Esenciales</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Necesarias para el funcionamiento básico del sitio web. No se pueden desactivar.
                  </p>
                </div>
                <div className="ml-4">
                  <div className="w-10 h-6 bg-primary rounded-full flex items-center justify-end px-1">
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </div>
              </div>
              
              {/* Cookies de análisis */}
              <div className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Cookies de Análisis
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Nos ayudan a entender cómo interactúas con nuestro sitio web (Google Analytics).
                  </p>
                </div>
                <div className="ml-4">
                  <button
                    onClick={toggleAnalytics}
                    className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                      consent.analytics ? 'bg-primary justify-end' : 'bg-gray-300 justify-start'
                    }`}
                  >
                    <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                  </button>
                </div>
              </div>
              
              {/* Cookies de marketing */}
              <div className="flex items-start justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">Cookies de Marketing</h4>
                  <p className="text-sm text-gray-600 mt-1">
                    Utilizadas para mostrar anuncios relevantes y medir la efectividad de nuestras campañas.
                  </p>
                </div>
                <div className="ml-4">
                  <button
                    onClick={toggleMarketing}
                    className={`w-10 h-6 rounded-full flex items-center transition-colors ${
                      consent.marketing ? 'bg-primary justify-end' : 'bg-gray-300 justify-start'
                    }`}
                  >
                    <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                  </button>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-2 pt-2">
              <button
                onClick={handleAcceptEssential}
                className="px-4 py-2 text-sm border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Solo esenciales
              </button>
              <button
                onClick={handleCustomSave}
                className="px-4 py-2 text-sm border border-primary text-primary hover:bg-primary-light rounded-md transition-colors"
              >
                Guardar preferencias
              </button>
              <button
                onClick={handleAcceptAll}
                className="px-4 py-2 text-sm bg-primary text-white hover:bg-primary-dark rounded-md transition-colors"
              >
                Aceptar todas
              </button>
            </div>
            
            <p className="text-xs text-gray-500 pt-2 border-t border-gray-200">
              Puedes cambiar tus preferencias en cualquier momento desde la configuración de privacidad.
              Para más información, consulta nuestra{' '}
              <a href="/politica-privacidad" className="text-primary hover:underline">
                Política de Privacidad
              </a>.
            </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
