# Implementación de Consentimiento de Cookies

## 📋 Resumen

Esta implementación cumple con los últimos lineamientos de Google SEO y las regulaciones de privacidad (GDPR, CCPA) para el manejo de cookies en CR Work.

## 🎯 Características Principales

### ✅ Cumplimiento Legal
- **GDPR Compliant**: Consentimiento explícito para cookies no esenciales
- **CCPA Compliant**: Opción de rechazar cookies opcionales
- **Google SEO**: No afecta Core Web Vitals ni indexación

### 🔧 Funcionalidades
- Banner de consentimiento no intrusivo
- Configuración granular de cookies
- Gestión de preferencias desde el footer
- Limpieza automática de cookies al revocar consentimiento
- Expiración automática del consentimiento (1 año)

## 📁 Archivos Implementados

```
src/
├── components/
│   ├── CookieConsent.tsx          # Banner principal de cookies
│   ├── CookiePreferences.tsx      # Modal de configuración detallada
│   └── Footer.tsx                 # Actualizado con enlace a preferencias
├── hooks/
│   └── useCookieConsent.ts        # Hook para gestión de estado
├── utils/
│   └── cookieUtils.ts             # Utilidades y funciones helper
└── lib/
    └── analytics.ts               # Actualizado con consentimiento
```

## 🚀 Uso

### Banner de Consentimiento
El banner aparece automáticamente en la primera visita y ofrece:
- **Solo esenciales**: Acepta únicamente cookies necesarias
- **Aceptar todas**: Acepta todas las categorías de cookies
- **Ver detalles**: Abre configuración granular

### Configuración Granular
Accesible desde el footer o el banner, permite controlar:
- ✅ **Cookies Esenciales** (siempre activas)
- 🔄 **Cookies de Análisis** (Google Analytics)
- 🎯 **Cookies de Marketing** (Google Ads, Facebook Pixel)

## 🔒 Categorías de Cookies

### Esenciales (Siempre Activas)
- Sesión de usuario
- Carrito de compras
- Preferencias de idioma
- Funcionalidad básica del sitio

### Análisis (Opcionales)
- **Google Analytics**: Métricas de uso y rendimiento
- **Datos recopilados**: Páginas visitadas, tiempo en sitio, fuente de tráfico
- **Propósito**: Mejorar la experiencia del usuario

### Marketing (Opcionales)
- **Google Ads**: Remarketing y conversiones
- **Facebook Pixel**: Seguimiento de campañas
- **Propósito**: Personalización de anuncios

## ⚙️ Configuración Técnica

### Google Analytics
```typescript
// Configuración automática basada en consentimiento
const config = {
  anonymize_ip: true,
  allow_google_signals: consent.marketing,
  analytics_storage: consent.analytics ? 'granted' : 'denied',
  ad_storage: consent.marketing ? 'granted' : 'denied'
};
```

### Limpieza de Cookies
```typescript
// Limpieza automática al revocar consentimiento
if (!consent.analytics) {
  // Eliminar cookies de Google Analytics
  cleanupGACookies();
}
```

## 📊 Eventos de Seguimiento

### Eventos Automáticos
- `cookie_consent_analytics_accepted`
- `cookie_consent_saved`
- `cookie_consent_revoked`

### Eventos Personalizados
```typescript
// Escuchar cambios de consentimiento
window.addEventListener('cookieConsentChanged', (event) => {
  const consent = event.detail;
  // Actualizar configuración
});
```

## 🎨 Diseño y UX

### Colores del Tema
- **Primario**: `#F39C12` (naranja CR Work)
- **Secundario**: `#1E293B` (gris oscuro)
- **Fondo**: Blanco con sombra sutil

### Posicionamiento
- **Banner**: Parte inferior, no intrusivo
- **Z-index**: 70 (por encima del contenido, debajo de modales)
- **Responsive**: Adaptado a móviles y desktop

## 🔄 Flujo de Usuario

1. **Primera visita**: Banner aparece automáticamente
2. **Decisión rápida**: "Solo esenciales" o "Aceptar todas"
3. **Configuración detallada**: Opcional via "Ver detalles"
4. **Gestión posterior**: Enlace en footer siempre disponible
5. **Expiración**: Renovación automática cada año

## 📱 Responsive Design

### Mobile
- Banner compacto con botones apilados
- Modal de preferencias optimizado para pantallas pequeñas
- Texto legible y botones táctiles

### Desktop
- Banner horizontal con botones en línea
- Modal centrado con información detallada
- Hover states y transiciones suaves

## 🛡️ Seguridad y Privacidad

### Datos Almacenados
```json
{
  "version": "1.0",
  "consent": {
    "essential": true,
    "analytics": false,
    "marketing": false,
    "timestamp": 1640995200000
  },
  "timestamp": 1640995200000
}
```

### Expiración
- **Duración**: 365 días
- **Renovación**: Automática al expirar
- **Versioning**: Control de cambios en políticas

## 🔧 Mantenimiento

### Actualizar Políticas
1. Cambiar `CONSENT_VERSION` en `cookieUtils.ts`
2. Los usuarios verán el banner nuevamente
3. Actualizar textos en componentes

### Agregar Nuevas Categorías
1. Extender interface `CookieConsent`
2. Actualizar componentes UI
3. Modificar lógica de Google Analytics

## 📈 Métricas y Monitoreo

### Eventos Disponibles
- Tasa de aceptación de cookies
- Preferencias más comunes
- Tiempo hasta decisión
- Cambios de preferencias

### Dashboard Recomendado
- Google Analytics: Eventos personalizados
- Seguimiento de conversiones
- Análisis de comportamiento

## 🚨 Consideraciones Legales

### GDPR (Europa)
- ✅ Consentimiento explícito
- ✅ Información clara sobre uso
- ✅ Fácil revocación
- ✅ Registro de consentimiento

### CCPA (California)
- ✅ Opción de opt-out
- ✅ Transparencia en recopilación
- ✅ No discriminación por rechazo

### Google SEO
- ✅ No bloquea contenido principal
- ✅ No afecta Core Web Vitals
- ✅ Carga asíncrona de scripts

## 🔄 Próximas Mejoras

1. **Geolocalización**: Mostrar banner solo en regiones requeridas
2. **A/B Testing**: Optimizar tasas de conversión
3. **Integración CMP**: Considerar plataforma de gestión de consentimiento
4. **Auditoría automática**: Reportes de cumplimiento

## 📞 Soporte

Para dudas sobre la implementación:
- Revisar documentación en código
- Consultar `cookieUtils.ts` para funciones helper
- Verificar eventos en DevTools

---

**Última actualización**: Enero 2025  
**Versión**: 1.0  
**Compatibilidad**: Chrome 90+, Firefox 88+, Safari 14+
