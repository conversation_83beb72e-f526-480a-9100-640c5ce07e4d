// Schema.org JSON-LD generators para SEO
import type { Product } from '../types';

// Schema para la organización (ya implementado en index.html, pero aquí para referencia)
export const generateOrganizationSchema = () => ({
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "CR Work Seguridad Industrial",
  "alternateName": "CR Work",
  "url": "https://crwork.nextba.com/#/",
  "logo": {
    "@type": "ImageObject",
    "url": "https://crwork.nextba.com/images/cr-work-logo.jpeg",
    "width": 300,
    "height": 100
  },
  "description": "Proveedor líder de equipos de protección personal (EPP), seguridad industrial, señalización e higiene en Argentina. Productos certificados de marcas reconocidas como 3M, MSA, Honeywell.",
  "foundingDate": "2020",
  "address": {
    "@type": "PostalAddress",
    "addressCountry": "AR",
    "addressRegion": "Buenos Aires"
  },
  "contactPoint": [
    {
      "@type": "ContactPoint",
      "contactType": "sales",
      "availableLanguage": ["Spanish"],
      "areaServed": "AR"
    }
  ],
  "sameAs": [
    "https://www.instagram.com/crwork.seguridad",
    "https://wa.me/5491234567890"
  ],
  "areaServed": {
    "@type": "Country",
    "name": "Argentina"
  },
  "knowsAbout": [
    "Equipos de Protección Personal",
    "Seguridad Industrial", 
    "Protección Personal",
    "Señalización Industrial",
    "EPP Certificados"
  ]
});

// Schema para productos individuales
export const generateProductSchema = (product: Product) => ({
  "@context": "https://schema.org",
  "@type": "Product",
  "name": product.name,
  "description": product.description || `${product.name} - Equipo de protección personal certificado`,
  "brand": {
    "@type": "Brand",
    "name": product.brand || "CR Work"
  },
  "category": product.category,
  "image": product.image_url || "https://cr-seg-ind.pages.dev/images/placeholder-product.jpg",
  "offers": {
    "@type": "Offer",
    "price": product.price,
    "priceCurrency": "ARS",
    "availability": product.stock && product.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
    "seller": {
      "@type": "Organization",
      "name": "CR Work Seguridad Industrial"
    },
    "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 días
  },
  "manufacturer": {
    "@type": "Organization",
    "name": product.brand || "CR Work"
  },
  "additionalProperty": [
    {
      "@type": "PropertyValue",
      "name": "Categoría EPP",
      "value": product.category
    },
    {
      "@type": "PropertyValue", 
      "name": "Certificación",
      "value": "Productos certificados según normativas argentinas"
    }
  ]
});

// Schema para páginas de categorías
export const generateCategorySchema = (categoryName: string, description: string, products: Product[]) => ({
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  "name": `${categoryName} - CR Work Seguridad Industrial`,
  "description": description,
  "url": `https://cr-seg-ind.pages.dev/categoria/${categoryName.toLowerCase().replace(/\s+/g, '-')}`,
  "mainEntity": {
    "@type": "ItemList",
    "name": `Productos de ${categoryName}`,
    "description": description,
    "numberOfItems": products.length,
    "itemListElement": products.slice(0, 10).map((product, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Product",
        "name": product.name,
        "url": `https://cr-seg-ind.pages.dev/producto/${product._id}`,
        "image": product.image_url,
        "offers": {
          "@type": "Offer",
          "price": product.price,
          "priceCurrency": "ARS"
        }
      }
    }))
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Inicio",
        "item": "https://cr-seg-ind.pages.dev/"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": categoryName,
        "item": `https://cr-seg-ind.pages.dev/categoria/${categoryName.toLowerCase().replace(/\s+/g, '-')}`
      }
    ]
  }
});

// Schema para página de inicio
export const generateWebPageSchema = () => ({
  "@context": "https://schema.org",
  "@type": "WebPage",
  "name": "EPP Seguridad Industrial | Protección Personal Certificada | CR Work",
  "description": "🛡️ EPP certificados para seguridad industrial. Cascos, guantes, anteojos 3M, MSA. Envío gratis +$50k. Entrega 24-48hs. Asesoramiento técnico especializado.",
  "url": "https://crwork.nextba.com/#/",
  "mainEntity": {
    "@type": "Organization",
    "name": "CR Work Seguridad Industrial"
  },
  "breadcrumb": {
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Inicio",
        "item": "https://cr-seg-ind.pages.dev/"
      }
    ]
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": "https://crwork.nextba.com/#/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
});

// Schema para FAQ (si tienes una sección de preguntas frecuentes)
export const generateFAQSchema = (faqs: Array<{question: string, answer: string}>) => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  "mainEntity": faqs.map(faq => ({
    "@type": "Question",
    "name": faq.question,
    "acceptedAnswer": {
      "@type": "Answer",
      "text": faq.answer
    }
  }))
});

// Schema para artículos/guías de seguridad industrial
export const generateArticleSchema = (title: string, description: string, datePublished: string) => ({
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": title,
  "description": description,
  "author": {
    "@type": "Organization",
    "name": "CR Work Seguridad Industrial"
  },
  "publisher": {
    "@type": "Organization",
    "name": "CR Work Seguridad Industrial",
    "logo": {
      "@type": "ImageObject",
      "url": "https://crwork.nextba.com/images/cr-work-logo.jpeg"
    }
  },
  "datePublished": datePublished,
  "dateModified": datePublished,
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://crwork.nextba.com/#/guia-seguridad-industrial"
  }
});

// Función helper para insertar schema en el DOM
export const insertSchema = (schema: object, id?: string) => {
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(schema);
  if (id) script.id = id;
  
  // Remover schema anterior si existe
  if (id) {
    const existing = document.getElementById(id);
    if (existing) existing.remove();
  }
  
  document.head.appendChild(script);
};

// Schemas específicos para categorías EPP
export const EPP_CATEGORY_SCHEMAS = {
  'proteccion-craneana': {
    name: 'Protección Craneana',
    description: 'Cascos de seguridad, cascos dieléctricos y protección para la cabeza. Productos certificados de marcas líderes como 3M, MSA y Honeywell para máxima protección en el trabajo.',
    keywords: ['cascos de seguridad', 'cascos dieléctricos', 'protección craneana', 'EPP cabeza']
  },
  'proteccion-visual': {
    name: 'Protección Visual',
    description: 'Anteojos de seguridad, gafas protectoras y protección ocular. Lentes certificados para proteger contra impactos, químicos y radiación UV en el ambiente laboral.',
    keywords: ['anteojos de seguridad', 'gafas protectoras', 'protección ocular', 'lentes seguridad']
  },
  'proteccion-respiratoria': {
    name: 'Protección Respiratoria',
    description: 'Máscaras, respiradores y filtros para protección respiratoria. Equipos certificados para proteger contra polvos, gases, vapores y contaminantes del aire.',
    keywords: ['máscaras respiratorias', 'respiradores', 'filtros aire', 'protección respiratoria']
  },
  'proteccion-auditiva': {
    name: 'Protección Auditiva',
    description: 'Protectores auditivos, tapones para oídos y orejeras de seguridad. Equipos para proteger la audición en ambientes con altos niveles de ruido.',
    keywords: ['protectores auditivos', 'tapones oídos', 'orejeras seguridad', 'protección auditiva']
  },
  'proteccion-manos': {
    name: 'Protección de Manos',
    description: 'Guantes de seguridad, guantes dieléctricos y protección para las manos. Amplia variedad para diferentes riesgos: cortes, químicos, calor y electricidad.',
    keywords: ['guantes de seguridad', 'guantes dieléctricos', 'protección manos', 'guantes trabajo']
  },
  'proteccion-pies': {
    name: 'Protección de Pies',
    description: 'Calzado de seguridad, botas dieléctricas y zapatos de trabajo. Protección contra impactos, perforaciones, químicos y riesgos eléctricos.',
    keywords: ['calzado de seguridad', 'botas dieléctricas', 'zapatos trabajo', 'protección pies']
  },
  'senalizacion': {
    name: 'Señalización Industrial',
    description: 'Carteles de seguridad, cintas de señalización y elementos de demarcación. Productos para señalizar riesgos, rutas de evacuación y zonas de trabajo.',
    keywords: ['carteles seguridad', 'señalización industrial', 'cintas demarcación', 'señales trabajo']
  }
};
