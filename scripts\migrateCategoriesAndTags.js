import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Mapeo de categorías antiguas a nueva estructura jerárquica
const categoryMigrationMap = {
  // Categorías de protección personal
  'Seguridad Industrial': ['epp', 'general'],
  'Protección': ['epp', 'general'],
  'Protección Respiratoria': ['epp', 'respiratoria'],
  'Protección Manual': ['epp', 'manos'],
  'Protección Craneana': ['epp', 'craneana'],
  'Protección Ocular': ['epp', 'ocular'],
  'Protección Facial': ['epp', 'facial'],
  'Protección Auditiva': ['epp', 'auditiva'],

  // Calzado
  'Calzado': ['epp', 'calzado'],
  'Calzado de Seguridad': ['epp', 'calzado', 'zapatos'],

  // Indumentaria
  'Indumentaria': ['epp', 'indumentaria'],
  'Indumentaria de Trabajo': ['epp', 'indumentaria', 'ropa-trabajo'],

  // Categorías actuales en la base de datos
  'EPP': ['epp', 'general'],
  'epp': ['epp', 'general'],
  'Todas': ['epp', 'general'],

  // Otros
  'Herramientas': ['herramientas', 'general'],
  'Equipos': ['equipos', 'general'],
  'Accesorios': ['accesorios', 'general']
};

// Mapeo de nombres de productos a categorías específicas
const productNameCategoryMap = {
  // Cascos y protección craneana
  'casco': ['epp', 'craneana'],
  'helmet': ['epp', 'craneana'],
  'dieléctrico': ['epp', 'craneana', 'craneana-dielectricos'],

  // Guantes
  'guantes': ['epp', 'manos'],
  'gloves': ['epp', 'manos'],

  // Calzado
  'botas': ['epp', 'calzado', 'botinas'],
  'zapatos': ['epp', 'calzado', 'zapatos'],
  'zapatillas': ['epp', 'calzado', 'zapatillas'],
  'boots': ['epp', 'calzado', 'botinas'],

  // Protección respiratoria
  'respirador': ['epp', 'respiratoria', 'mascarillas-descartables'],
  'mascarilla': ['epp', 'respiratoria', 'mascarillas-descartables'],
  'máscara': ['epp', 'respiratoria', 'mascaras'],
  'barbijo': ['epp', 'respiratoria', 'mascarillas-descartables'],

  // Protección ocular
  'anteojos': ['epp', 'ocular', 'anteojos-seguridad'],
  'gafas': ['epp', 'ocular', 'anteojos-seguridad'],
  'lentes': ['epp', 'ocular', 'anteojos-seguridad'],

  // Indumentaria (basado en productos reales)
  'ambo': ['epp', 'indumentaria', 'ropa-trabajo'],
  'mameluco': ['epp', 'indumentaria', 'mamelucos'],
  'camisa': ['epp', 'indumentaria', 'ropa-trabajo'],
  'campera': ['epp', 'indumentaria', 'ropa-trabajo'],
  'trucker': ['epp', 'indumentaria', 'ropa-trabajo'],
  'pantalón': ['epp', 'indumentaria', 'ropa-trabajo'],
  'chaleco': ['epp', 'indumentaria', 'chalecos-reflectivos'],
  'buzo': ['epp', 'indumentaria', 'ropa-trabajo'],
  'remera': ['epp', 'indumentaria', 'ropa-trabajo'],

  // Protección auditiva
  'protector auditivo': ['epp', 'auditiva', 'tapones'],
  'tapones': ['epp', 'auditiva', 'tapones'],
  'auriculares': ['epp', 'auditiva', 'auriculares']
};

// Tags por categoría
const categoryTagsMap = {
  'craneana': ['seguridad', 'cabeza', 'impacto', 'construccion', 'casco'],
  'ocular': ['vision', 'proteccion', 'ojos', 'transparente', 'laboratorio'],
  'facial': ['cara', 'proteccion', 'impacto', 'quimica', 'soldadura'],
  'respiratoria': ['respiracion', 'filtro', 'particulas', 'gases', 'vapores'],
  'indumentaria': ['ropa', 'trabajo', 'proteccion', 'industrial', 'uniforme'],
  'manos': ['guantes', 'proteccion', 'agarre', 'cortes', 'quimica'],
  'calzado': ['pies', 'seguridad', 'puntera', 'antideslizante', 'industrial'],
  'auditiva': ['oidos', 'ruido', 'sonido', 'proteccion', 'industrial'],
  'general': ['epp', 'seguridad', 'industrial', 'proteccion', 'trabajo']
};

// Tags por marca
const brandTagsMap = {
  '3M': ['innovacion', 'calidad', 'tecnologia', 'confiable'],
  'MSA': ['profesional', 'resistente', 'certificado'],
  'HONEYWELL': ['precision', 'durabilidad', 'ergonomico'],
  'ANSELL': ['especializado', 'quimica', 'laboratorio'],
  'UVEX': ['vision', 'comodidad', 'deportivo'],
  'STEELPRO': ['resistente', 'industrial', 'duradero'],
  'PAMPERO': ['nacional', 'trabajo', 'confiable']
};

// Mapeo de industrias
const industryMap = {
  'MANUFACTURA': 'MANUFACTURA',
  'CONSTRUCCION': 'CONSTRUCCION',
  'ELECTRICIDAD': 'ELECTRICIDAD',
  'MINERIA': 'MINERIA',
  'QUIMICA': 'QUIMICA',
  'ALIMENTARIA': 'ALIMENTARIA',
  'SALUD': 'SALUD',
  'GENERAL': 'GENERAL'
};

/**
 * Determina las categorías para un producto basándose en su categoría actual y nombre
 */
function determineProductCategories(product) {
  const productName = product.name.toLowerCase();
  const currentCategories = product.categories || [];

  // Primero intentar mapeo por nombre de producto
  for (const [keyword, categories] of Object.entries(productNameCategoryMap)) {
    if (productName.includes(keyword)) {
      return categories;
    }
  }

  // Si ya tiene categorías pero son muy básicas, intentar mejorarlas
  if (currentCategories.length > 0) {
    const firstCategory = currentCategories[0];
    if (categoryMigrationMap[firstCategory]) {
      return categoryMigrationMap[firstCategory];
    }

    // Si es EPP genérico, intentar ser más específico basándose en el nombre
    if (firstCategory === 'EPP' || firstCategory === 'epp') {
      for (const [keyword, categories] of Object.entries(productNameCategoryMap)) {
        if (productName.includes(keyword)) {
          return categories;
        }
      }
      // Mantener EPP pero agregar subcategoría general
      return ['epp', 'general'];
    }
  }

  // Fallback: categoría general
  return ['epp', 'general'];
}

/**
 * Genera tags para un producto
 */
function generateProductTags(product, categories) {
  const tags = new Set();
  
  // Tags basados en categorías
  categories.forEach(category => {
    if (categoryTagsMap[category]) {
      categoryTagsMap[category].forEach(tag => tags.add(tag));
    }
  });
  
  // Tags basados en marca
  if (product.brand && brandTagsMap[product.brand]) {
    brandTagsMap[product.brand].forEach(tag => tags.add(tag));
  }
  
  // Tags basados en nombre del producto
  const productName = product.name.toLowerCase();
  if (productName.includes('dieléctrico') || productName.includes('dielectrico')) {
    tags.add('dielectrico');
  }
  if (productName.includes('antideslizante')) {
    tags.add('antideslizante');
  }
  if (productName.includes('resistente')) {
    tags.add('resistente');
  }
  if (productName.includes('impermeable')) {
    tags.add('impermeable');
  }
  
  // Tags basados en industria
  if (product.industry) {
    tags.add(product.industry.toLowerCase());
  }
  
  return Array.from(tags).slice(0, 10); // Limitar a 10 tags
}

/**
 * Genera propiedades para un producto
 */
function generateProductProperties(product, categories) {
  const properties = {};
  
  // Propiedades básicas
  if (product.brand) {
    properties.marca = product.brand;
  }
  
  if (product.industry) {
    properties.industria = product.industry;
  }
  
  // Propiedades específicas por categoría
  const mainCategory = categories[1] || categories[0]; // Segunda categoría o primera si no hay segunda
  
  switch (mainCategory) {
    case 'craneana':
      properties.tipo_proteccion = 'Craneana';
      properties.normativa = ['ANSI Z89.1', 'EN 397'];
      properties.resistencia_impacto = 'Alta';
      break;
      
    case 'ocular':
      properties.tipo_proteccion = 'Ocular';
      properties.normativa = ['ANSI Z87.1', 'EN 166'];
      properties.resistencia_impacto = 'Media';
      break;
      
    case 'respiratoria':
      properties.tipo_proteccion = 'Respiratoria';
      properties.normativa = ['NIOSH', 'EN 149'];
      properties.eficiencia = '95%';
      break;
      
    case 'manos':
      properties.tipo_proteccion = 'Manual';
      properties.normativa = ['EN 388', 'ANSI/ISEA 105'];
      break;
      
    case 'calzado':
      properties.tipo_proteccion = 'Calzado';
      properties.normativa = ['ASTM F2413', 'EN ISO 20345'];
      properties.puntera = 'Acero';
      break;
      
    case 'indumentaria':
      properties.tipo_proteccion = 'Corporal';
      properties.normativa = ['EN ISO 13688'];
      break;
  }
  
  // Extraer peso si está en especificaciones
  if (product.especificaciones) {
    const pesoMatch = product.especificaciones.match(/(\d+)\s*g/i);
    if (pesoMatch) {
      properties.peso = parseInt(pesoMatch[1]);
    }
  }
  
  return properties;
}

/**
 * Normaliza la industria del producto
 */
function normalizeIndustry(industry) {
  if (!industry) return ['GENERAL'];
  
  const upperIndustry = industry.toUpperCase();
  if (industryMap[upperIndustry]) {
    return [industryMap[upperIndustry]];
  }
  
  return ['GENERAL'];
}

/**
 * Función principal de migración
 */
async function migrateCategoriesAndTags() {
  console.log('🚀 Iniciando migración de categorías y tags...');
  
  try {
    // 1. Obtener todos los productos
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('*');

    if (fetchError) {
      throw fetchError;
    }

    console.log(`📦 Encontrados ${products.length} productos para migrar`);

    let updatedCount = 0;
    let errorCount = 0;

    // 2. Procesar cada producto
    for (const product of products) {
      try {
        console.log(`\n🔄 Procesando: ${product.name}`);
        
        // Determinar nuevas categorías
        const newCategories = determineProductCategories(product);
        console.log(`   📂 Categorías: ${newCategories.join(' → ')}`);
        
        // Generar tags
        const tags = generateProductTags(product, newCategories);
        console.log(`   🏷️  Tags: ${tags.join(', ')}`);
        
        // Generar propiedades
        const properties = generateProductProperties(product, newCategories);
        console.log(`   ⚙️  Propiedades: ${Object.keys(properties).length} items`);
        
        // Normalizar industrias
        const industries = normalizeIndustry(product.industry);
        console.log(`   🏭 Industrias: ${industries.join(', ')}`);
        
        // Actualizar el producto
        const { error: updateError } = await supabase
          .from('products')
          .update({
            categories: newCategories,
            industries: industries,
            tags: tags,
            properties: properties
          })
          .eq('id', product.id);

        if (updateError) {
          throw updateError;
        }

        updatedCount++;
        console.log(`   ✅ Actualizado exitosamente`);

      } catch (error) {
        errorCount++;
        console.error(`   ❌ Error procesando ${product.name}:`, error.message);
      }
    }

    console.log(`\n📊 Resumen de migración:`);
    console.log(`   ✅ Productos actualizados: ${updatedCount}`);
    console.log(`   ❌ Errores: ${errorCount}`);
    console.log(`   📦 Total procesados: ${products.length}`);

  } catch (error) {
    console.error('💥 Error general en la migración:', error);
    throw error;
  }
}

// Ejecutar la migración
migrateCategoriesAndTags()
  .then(() => {
    console.log('\n🎉 ¡Migración completada exitosamente!');
    console.log('💡 Los filtros de categorías ahora deberían funcionar correctamente');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error fatal en la migración:', error);
    process.exit(1);
  });

export default migrateCategoriesAndTags;
