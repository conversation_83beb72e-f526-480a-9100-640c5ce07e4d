import { useState, useEffect, useCallback } from 'react';

interface CookieConsent {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
}

interface CookieConsentData {
  version: string;
  consent: CookieConsent;
  timestamp: number;
}

const COOKIE_CONSENT_KEY = 'cr-work-cookie-consent';
const CONSENT_VERSION = '1.0';
const CONSENT_EXPIRY_DAYS = 365; // 1 año

export const useCookieConsent = () => {
  const [consent, setConsent] = useState<CookieConsent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [needsConsent, setNeedsConsent] = useState(false);

  // Verificar si el consentimiento ha expirado
  const isConsentExpired = useCallback((timestamp: number): boolean => {
    const expiryTime = timestamp + (CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
    return Date.now() > expiryTime;
  }, []);

  // Cargar consentimiento guardado
  useEffect(() => {
    const loadSavedConsent = () => {
      try {
        const savedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
        
        if (savedConsent) {
          const parsed: CookieConsentData = JSON.parse(savedConsent);
          
          // Verificar versión y expiración
          if (
            parsed.version === CONSENT_VERSION && 
            !isConsentExpired(parsed.timestamp)
          ) {
            setConsent(parsed.consent);
            setNeedsConsent(false);
          } else {
            // Consentimiento expirado o versión antigua
            localStorage.removeItem(COOKIE_CONSENT_KEY);
            setNeedsConsent(true);
          }
        } else {
          setNeedsConsent(true);
        }
      } catch (error) {
        console.error('Error loading cookie consent:', error);
        localStorage.removeItem(COOKIE_CONSENT_KEY);
        setNeedsConsent(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadSavedConsent();
  }, [isConsentExpired]);

  // Guardar consentimiento
  const saveConsent = useCallback((newConsent: CookieConsent) => {
    const consentData: CookieConsentData = {
      version: CONSENT_VERSION,
      consent: newConsent,
      timestamp: Date.now()
    };
    
    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(consentData));
      setConsent(newConsent);
      setNeedsConsent(false);
      
      // Disparar evento personalizado para notificar cambios
      window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
        detail: newConsent
      }));
      
      return true;
    } catch (error) {
      console.error('Error saving cookie consent:', error);
      return false;
    }
  }, []);

  // Revocar consentimiento
  const revokeConsent = useCallback(() => {
    localStorage.removeItem(COOKIE_CONSENT_KEY);
    setConsent(null);
    setNeedsConsent(true);
    
    // Disparar evento de revocación
    window.dispatchEvent(new CustomEvent('cookieConsentRevoked'));
  }, []);

  // Verificar si un tipo específico de cookie está permitido
  const hasConsent = useCallback((type: keyof CookieConsent): boolean => {
    return consent?.[type] ?? false;
  }, [consent]);

  // Verificar si se puede usar Google Analytics
  const canUseAnalytics = useCallback((): boolean => {
    return hasConsent('analytics');
  }, [hasConsent]);

  // Verificar si se pueden usar cookies de marketing
  const canUseMarketing = useCallback((): boolean => {
    return hasConsent('marketing');
  }, [hasConsent]);

  // Obtener configuración para Google Analytics
  const getAnalyticsConfig = useCallback(() => {
    if (!consent) return null;
    
    return {
      anonymize_ip: true,
      allow_google_signals: consent.marketing,
      allow_ad_personalization_signals: consent.marketing,
      cookie_expires: CONSENT_EXPIRY_DAYS * 24 * 60 * 60, // en segundos
      storage: consent.analytics ? 'granted' : 'denied',
      analytics_storage: consent.analytics ? 'granted' : 'denied',
      ad_storage: consent.marketing ? 'granted' : 'denied',
      ad_user_data: consent.marketing ? 'granted' : 'denied',
      ad_personalization: consent.marketing ? 'granted' : 'denied'
    };
  }, [consent]);

  // Presets de consentimiento
  const acceptAll = useCallback(() => {
    const allConsent: CookieConsent = {
      essential: true,
      analytics: true,
      marketing: true,
      timestamp: Date.now()
    };
    return saveConsent(allConsent);
  }, [saveConsent]);

  const acceptEssentialOnly = useCallback(() => {
    const essentialConsent: CookieConsent = {
      essential: true,
      analytics: false,
      marketing: false,
      timestamp: Date.now()
    };
    return saveConsent(essentialConsent);
  }, [saveConsent]);

  const acceptAnalyticsOnly = useCallback(() => {
    const analyticsConsent: CookieConsent = {
      essential: true,
      analytics: true,
      marketing: false,
      timestamp: Date.now()
    };
    return saveConsent(analyticsConsent);
  }, [saveConsent]);

  return {
    // Estado
    consent,
    isLoading,
    needsConsent,
    
    // Acciones
    saveConsent,
    revokeConsent,
    acceptAll,
    acceptEssentialOnly,
    acceptAnalyticsOnly,
    
    // Verificaciones
    hasConsent,
    canUseAnalytics,
    canUseMarketing,
    
    // Configuración
    getAnalyticsConfig,
    
    // Constantes
    CONSENT_VERSION,
    CONSENT_EXPIRY_DAYS
  };
};

export type { CookieConsent, CookieConsentData };
