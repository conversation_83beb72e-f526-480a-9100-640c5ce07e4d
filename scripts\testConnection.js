import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

console.log('🔍 Probando conexión a Supabase...');
console.log('URL:', process.env.VITE_SUPABASE_URL);
console.log('Service Role Key:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Configurada' : 'No configurada');

const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

async function testConnection() {
  try {
    console.log('\n📡 Probando conexión...');
    
    // Test básico de conexión
    const { data, error } = await supabase
      .from('products')
      .select('count', { count: 'exact', head: true });
    
    if (error) {
      console.error('❌ Error de conexión:', error.message);
      return false;
    }
    
    console.log('✅ Conexión exitosa');
    console.log(`📦 Total de productos en la base de datos: ${data || 0}`);
    
    // Probar una consulta simple
    const { data: sampleProducts, error: sampleError } = await supabase
      .from('products')
      .select('id, name, categories, tags, industries, properties')
      .limit(3);
    
    if (sampleError) {
      console.error('❌ Error obteniendo productos:', sampleError.message);
      return false;
    }
    
    console.log('\n📋 Productos de ejemplo:');
    sampleProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.name}`);
      console.log(`   Categorías: ${product.categories ? JSON.stringify(product.categories) : 'null'}`);
      console.log(`   Tags: ${product.tags ? JSON.stringify(product.tags) : 'null'}`);
      console.log(`   Industrias: ${product.industries ? JSON.stringify(product.industries) : 'null'}`);
      console.log(`   Propiedades: ${product.properties ? Object.keys(product.properties).length + ' items' : 'null'}`);
      console.log('');
    });
    
    return true;
    
  } catch (error) {
    console.error('💥 Error general:', error.message);
    return false;
  }
}

testConnection()
  .then((success) => {
    if (success) {
      console.log('🎉 Prueba de conexión exitosa');
    } else {
      console.log('❌ Prueba de conexión falló');
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  });
