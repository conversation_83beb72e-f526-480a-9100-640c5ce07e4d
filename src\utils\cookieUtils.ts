/**
 * Utilidades para el manejo de cookies y consentimiento
 * Cumple con GDPR, CCPA y lineamientos de Google SEO
 */

export interface CookieConsent {
  essential: boolean;
  analytics: boolean;
  marketing: boolean;
  timestamp: number;
}

export interface CookieConsentData {
  version: string;
  consent: CookieConsent;
  timestamp: number;
}

// Constantes
export const COOKIE_CONSENT_KEY = 'cr-work-cookie-consent';
export const CONSENT_VERSION = '1.0';
export const CONSENT_EXPIRY_DAYS = 365; // 1 año

/**
 * Verifica si el consentimiento ha expirado
 */
export const isConsentExpired = (timestamp: number): boolean => {
  const expiryTime = timestamp + (CONSENT_EXPIRY_DAYS * 24 * 60 * 60 * 1000);
  return Date.now() > expiryTime;
};

/**
 * Obtiene el consentimiento guardado en localStorage
 */
export const getSavedConsent = (): CookieConsent | null => {
  try {
    const saved = localStorage.getItem(COOKIE_CONSENT_KEY);
    if (!saved) return null;

    const parsed: CookieConsentData = JSON.parse(saved);
    
    // Verificar versión y expiración
    if (parsed.version !== CONSENT_VERSION || isConsentExpired(parsed.timestamp)) {
      localStorage.removeItem(COOKIE_CONSENT_KEY);
      return null;
    }

    return parsed.consent;
  } catch (error) {
    console.error('Error parsing saved consent:', error);
    localStorage.removeItem(COOKIE_CONSENT_KEY);
    return null;
  }
};

/**
 * Guarda el consentimiento en localStorage
 */
export const saveConsent = (consent: CookieConsent): boolean => {
  try {
    const data: CookieConsentData = {
      version: CONSENT_VERSION,
      consent,
      timestamp: Date.now()
    };

    localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify(data));
    
    // Disparar evento personalizado
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: consent
    }));

    return true;
  } catch (error) {
    console.error('Error saving consent:', error);
    return false;
  }
};

/**
 * Revoca todo el consentimiento
 */
export const revokeConsent = (): void => {
  localStorage.removeItem(COOKIE_CONSENT_KEY);
  
  // Disparar evento de revocación
  window.dispatchEvent(new CustomEvent('cookieConsentRevoked'));
};

/**
 * Verifica si un tipo específico de cookie está permitido
 */
export const hasConsentFor = (type: keyof CookieConsent): boolean => {
  const consent = getSavedConsent();
  return consent?.[type] ?? false;
};

/**
 * Presets de consentimiento comunes
 */
export const CONSENT_PRESETS = {
  ALL: {
    essential: true,
    analytics: true,
    marketing: true,
    timestamp: Date.now()
  } as CookieConsent,
  
  ESSENTIAL_ONLY: {
    essential: true,
    analytics: false,
    marketing: false,
    timestamp: Date.now()
  } as CookieConsent,
  
  ANALYTICS_ONLY: {
    essential: true,
    analytics: true,
    marketing: false,
    timestamp: Date.now()
  } as CookieConsent
};

/**
 * Configuración para Google Analytics basada en el consentimiento
 */
export const getGoogleAnalyticsConfig = (consent: CookieConsent) => {
  return {
    anonymize_ip: true,
    allow_google_signals: consent.marketing,
    allow_ad_personalization_signals: consent.marketing,
    cookie_expires: CONSENT_EXPIRY_DAYS * 24 * 60 * 60, // en segundos
    storage: consent.analytics ? 'granted' : 'denied',
    analytics_storage: consent.analytics ? 'granted' : 'denied',
    ad_storage: consent.marketing ? 'granted' : 'denied',
    ad_user_data: consent.marketing ? 'granted' : 'denied',
    ad_personalization: consent.marketing ? 'granted' : 'denied'
  };
};

/**
 * Configuración de consentimiento para gtag
 */
export const getGtagConsentConfig = (consent: CookieConsent) => {
  return {
    'analytics_storage': consent.analytics ? 'granted' : 'denied',
    'ad_storage': consent.marketing ? 'granted' : 'denied',
    'ad_user_data': consent.marketing ? 'granted' : 'denied',
    'ad_personalization': consent.marketing ? 'granted' : 'denied'
  };
};

/**
 * Limpia cookies específicas cuando se revoca el consentimiento
 */
export const cleanupCookies = (consent: CookieConsent): void => {
  // Si se revoca el consentimiento de analytics, limpiar cookies de GA
  if (!consent.analytics) {
    const gaCookies = document.cookie
      .split(';')
      .filter(cookie => cookie.trim().startsWith('_ga'))
      .map(cookie => cookie.split('=')[0].trim());

    gaCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
    });
  }

  // Si se revoca el consentimiento de marketing, limpiar cookies relacionadas
  if (!consent.marketing) {
    const marketingCookies = ['_fbp', '_fbc', '__utma', '__utmb', '__utmc', '__utmz'];
    
    marketingCookies.forEach(cookieName => {
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname};`;
    });
  }
};

/**
 * Verifica si necesita mostrar el banner de cookies
 */
export const needsConsentBanner = (): boolean => {
  return getSavedConsent() === null;
};

/**
 * Obtiene información sobre las cookies utilizadas
 */
export const getCookieInfo = () => {
  return {
    essential: {
      name: 'Cookies Esenciales',
      description: 'Necesarias para el funcionamiento básico del sitio web',
      examples: ['Sesión de usuario', 'Carrito de compras', 'Preferencias de idioma'],
      canDisable: false
    },
    analytics: {
      name: 'Cookies de Análisis',
      description: 'Nos ayudan a entender cómo interactúas con nuestro sitio web',
      examples: ['Google Analytics', 'Métricas de rendimiento'],
      services: ['Google Analytics'],
      canDisable: true
    },
    marketing: {
      name: 'Cookies de Marketing',
      description: 'Utilizadas para mostrar anuncios relevantes y medir efectividad',
      examples: ['Google Ads', 'Facebook Pixel', 'Remarketing'],
      services: ['Google Ads', 'Facebook Pixel'],
      canDisable: true
    }
  };
};

/**
 * Genera un reporte de consentimiento para auditorías
 */
export const generateConsentReport = (): string => {
  const consent = getSavedConsent();
  const info = getCookieInfo();
  
  if (!consent) {
    return 'No hay consentimiento registrado';
  }

  const report = {
    timestamp: new Date(consent.timestamp).toISOString(),
    version: CONSENT_VERSION,
    consent: {
      essential: consent.essential,
      analytics: consent.analytics,
      marketing: consent.marketing
    },
    services: {
      googleAnalytics: consent.analytics,
      googleAds: consent.marketing,
      facebookPixel: consent.marketing
    }
  };

  return JSON.stringify(report, null, 2);
};
