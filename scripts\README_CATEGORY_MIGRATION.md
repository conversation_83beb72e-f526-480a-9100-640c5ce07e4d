# Migración de Categorías y Filtros

Este directorio contiene scripts para migrar y configurar el sistema de categorías y filtros de productos.

## Problema Identificado

Los filtros de categorías no funcionan correctamente porque:

1. **Estructura inconsistente**: Los productos tienen categorías simples pero el frontend espera arrays jerárquicos
2. **Falta de datos**: Los productos no tienen `tags` ni `properties` asignados
3. **Mapeo incompleto**: No hay correspondencia entre categorías antiguas y la nueva estructura

## Solución Implementada

### Scripts Disponibles

#### 1. `verifyDatabaseStructure.js`
Verifica el estado actual de la base de datos y identifica problemas.

```bash
node scripts/verifyDatabaseStructure.js
```

**Qué hace:**
- Verifica que la tabla `products` tenga las columnas necesarias
- Identifica productos sin categorías, tags o propiedades
- Prueba la función de búsqueda avanzada
- Genera estadísticas detalladas

#### 2. `migrateCategoriesAndTags.js`
Migra las categorías existentes a la nueva estructura jerárquica.

```bash
node scripts/migrateCategoriesAndTags.js
```

**Qué hace:**
- Mapea categorías antiguas a la nueva estructura EPP
- Asigna categorías basándose en nombres de productos
- Genera tags automáticamente
- Crea propiedades específicas por tipo de producto
- Normaliza industrias

#### 3. `setupCategoryFilters.js`
Script principal que ejecuta todo el proceso de configuración.

```bash
node scripts/setupCategoryFilters.js
```

**Qué hace:**
- Ejecuta verificación de estructura
- Ejecuta migración de categorías
- Prueba el funcionamiento de filtros
- Genera reporte final

## Instrucciones de Uso

### Paso 1: Preparación
Asegúrate de tener las variables de entorno configuradas:

```bash
# .env
VITE_SUPABASE_URL=tu_supabase_url
SUPABASE_SERVICE_ROLE_KEY=tu_service_role_key
```

### Paso 2: Verificar Estado Actual
```bash
node scripts/verifyDatabaseStructure.js
```

### Paso 3: Ejecutar Migración Completa
```bash
node scripts/setupCategoryFilters.js
```

O ejecutar pasos individuales:
```bash
# Solo migración
node scripts/migrateCategoriesAndTags.js
```

### Paso 4: Verificar Resultados
Después de la migración, verifica que:
- Los filtros de categorías funcionen en la aplicación
- La búsqueda devuelva resultados apropiados
- Los productos tengan categorías asignadas

## Estructura de Categorías

### Nueva Estructura Jerárquica
```
epp (Equipos de Protección Personal)
├── craneana (Protección Craneana)
│   ├── craneana-dielectricos
│   ├── craneana-altura
│   └── craneana-general
├── ocular (Protección Ocular)
├── respiratoria (Protección Respiratoria)
├── manos (Protección Manual)
├── calzado (Protección de Pies)
├── indumentaria (Protección Corporal)
└── auditiva (Protección Auditiva)
```

### Mapeo de Categorías Antiguas
```javascript
'Seguridad Industrial' → ['epp', 'general']
'Protección Respiratoria' → ['epp', 'respiratoria']
'Calzado de Seguridad' → ['epp', 'calzado', 'zapatos']
'Indumentaria de Trabajo' → ['epp', 'indumentaria', 'ropa-trabajo']
```

## Sistema de Tags

### Tags por Categoría
- **Craneana**: seguridad, cabeza, impacto, construccion, casco
- **Ocular**: vision, proteccion, ojos, transparente, laboratorio
- **Respiratoria**: respiracion, filtro, particulas, gases, vapores
- **Manual**: guantes, proteccion, agarre, cortes, quimica

### Tags por Marca
- **3M**: innovacion, calidad, tecnologia, confiable
- **MSA**: profesional, resistente, certificado
- **HONEYWELL**: precision, durabilidad, ergonomico

## Sistema de Propiedades

### Propiedades por Categoría
```javascript
// Protección Craneana
{
  tipo_proteccion: 'Craneana',
  normativa: ['ANSI Z89.1', 'EN 397'],
  resistencia_impacto: 'Alta'
}

// Protección Respiratoria
{
  tipo_proteccion: 'Respiratoria',
  normativa: ['NIOSH', 'EN 149'],
  eficiencia: '95%'
}
```

## Resolución de Problemas

### Error: "Columnas faltantes"
Si aparece este error, ejecuta la migración de base de datos:
```sql
-- En Supabase SQL Editor
\i supabase/migrations/add_product_tags_and_properties.sql
```

### Error: "Función search_products_advanced no disponible"
La función se crea automáticamente con la migración. Si persiste el error:
1. Verifica que la migración se ejecutó correctamente
2. Revisa los logs de Supabase
3. Ejecuta manualmente la migración

### Productos sin categorías después de migración
Esto puede ocurrir si:
1. El producto no coincide con ningún patrón de mapeo
2. El nombre del producto no contiene palabras clave reconocibles

**Solución**: Edita `productNameCategoryMap` en `migrateCategoriesAndTags.js` para agregar más patrones.

## Validación Post-Migración

### Verificar en la Aplicación
1. Ve a la página del catálogo
2. Usa los filtros de categorías
3. Verifica que se muestren productos
4. Prueba la búsqueda por texto

### Verificar en Base de Datos
```sql
-- Productos con categorías
SELECT name, categories FROM products WHERE categories != '{}';

-- Productos con tags
SELECT name, tags FROM products WHERE tags != '{}';

-- Distribución por categorías
SELECT categories[1] as main_category, COUNT(*) 
FROM products 
WHERE categories != '{}' 
GROUP BY categories[1];
```

## Mantenimiento

### Agregar Nuevos Productos
Cuando agregues nuevos productos, asegúrate de:
1. Asignar categorías apropiadas usando la estructura jerárquica
2. Incluir tags relevantes
3. Definir propiedades específicas del tipo de producto

### Actualizar Mapeos
Si necesitas actualizar los mapeos:
1. Edita `categoryMigrationMap` o `productNameCategoryMap`
2. Ejecuta la migración nuevamente
3. Verifica los resultados

## Soporte

Si encuentras problemas:
1. Revisa los logs de la consola
2. Verifica las variables de entorno
3. Asegúrate de que Supabase esté accesible
4. Consulta la documentación de Supabase para problemas de conectividad
