/* Optimizaciones de imágenes responsivas para SEO y performance */

/* Imágenes fluidas por defecto */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Contenedores de imágenes responsivas */
.responsive-image-container {
  position: relative;
  overflow: hidden;
}

.responsive-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

/* Aspect ratios para diferentes tipos de imágenes */
.aspect-ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.aspect-ratio-4-3 {
  aspect-ratio: 4 / 3;
}

.aspect-ratio-1-1 {
  aspect-ratio: 1 / 1;
}

.aspect-ratio-3-2 {
  aspect-ratio: 3 / 2;
}

/* Optimizaciones específicas para productos EPP */
.product-image {
  aspect-ratio: 1 / 1;
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  padding: 8px;
  transition: transform 0.3s ease, filter 0.3s ease;
}

.product-image:hover img {
  transform: scale(1.05);
}

/* Hero image optimizations */
.hero-background {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Responsive breakpoints para imágenes */
@media (max-width: 640px) {
  .hero-background {
    background-attachment: scroll; /* Mejor performance en móviles */
  }
  
  .product-image img {
    padding: 4px;
  }
}

@media (max-width: 768px) {
  /* Optimizaciones para tablet */
  .responsive-image-container {
    border-radius: 6px;
  }
}

@media (min-width: 1024px) {
  /* Optimizaciones para desktop */
  .product-image:hover img {
    transform: scale(1.08);
  }
}

/* Lazy loading placeholder */
.image-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Optimizaciones para imágenes de categorías */
.category-image {
  aspect-ratio: 16 / 9;
  background-color: #f1f5f9;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.category-image:hover img {
  transform: scale(1.03);
}

/* Overlay para mejorar legibilidad de texto sobre imágenes */
.image-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  pointer-events: none;
}

/* Optimizaciones para logos y iconos */
.logo-image {
  max-height: 60px;
  width: auto;
  object-fit: contain;
}

@media (max-width: 640px) {
  .logo-image {
    max-height: 40px;
  }
}

/* Imágenes de marca/cliente */
.brand-image {
  max-height: 80px;
  width: auto;
  object-fit: contain;
  filter: grayscale(100%) opacity(0.7);
  transition: filter 0.3s ease;
}

.brand-image:hover {
  filter: grayscale(0%) opacity(1);
}

/* Optimizaciones para WebP y formatos modernos */
.modern-image {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Contenedor para imágenes con loading states */
.image-container {
  position: relative;
  display: inline-block;
}

.image-container.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  z-index: 1;
}

/* Optimizaciones para Core Web Vitals */
.critical-image {
  /* Prioridad alta para imágenes above-the-fold */
  content-visibility: auto;
  contain-intrinsic-size: 300px 200px;
}

/* Responsive typography para alt text y captions */
.image-caption {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
  text-align: center;
}

@media (max-width: 640px) {
  .image-caption {
    font-size: 0.75rem;
  }
}

/* Accesibilidad - focus states para imágenes interactivas */
.interactive-image {
  cursor: pointer;
  transition: all 0.3s ease;
}

.interactive-image:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.interactive-image:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
