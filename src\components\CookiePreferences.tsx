import React, { useState } from 'react';
import { Settings, Shield, BarChart3, Target, X } from 'lucide-react';
import { useCookieConsent } from '../hooks/useCookieConsent';
import { updateConsent } from '../lib/analytics';

interface CookiePreferencesProps {
  isOpen: boolean;
  onClose: () => void;
}

const CookiePreferences: React.FC<CookiePreferencesProps> = ({ isOpen, onClose }) => {
  const { consent, saveConsent, revokeConsent } = useCookieConsent();
  const [localConsent, setLocalConsent] = useState(consent || {
    essential: true,
    analytics: false,
    marketing: false,
    timestamp: Date.now()
  });

  if (!isOpen) return null;

  const handleSave = () => {
    const success = saveConsent(localConsent);
    if (success) {
      updateConsent({
        analytics: localConsent.analytics,
        marketing: localConsent.marketing
      });
      onClose();
    }
  };

  const handleRevokeAll = () => {
    revokeConsent();
    setLocalConsent({
      essential: true,
      analytics: false,
      marketing: false,
      timestamp: Date.now()
    });
    updateConsent({
      analytics: false,
      marketing: false
    });
    onClose();
  };

  const toggleAnalytics = () => {
    setLocalConsent(prev => ({ ...prev, analytics: !prev.analytics }));
  };

  const toggleMarketing = () => {
    setLocalConsent(prev => ({ ...prev, marketing: !prev.marketing }));
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[80] p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
              <Settings className="h-6 w-6 text-primary" />
              Preferencias de Cookies
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Descripción */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800">
              Gestiona tus preferencias de cookies. Puedes cambiar estas configuraciones en cualquier momento. 
              Las cookies esenciales son necesarias para el funcionamiento del sitio y no se pueden desactivar.
            </p>
          </div>

          {/* Configuraciones de cookies */}
          <div className="space-y-4">
            {/* Cookies Esenciales */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    Cookies Esenciales
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      Siempre activas
                    </span>
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Estas cookies son necesarias para que el sitio web funcione correctamente. 
                    Incluyen funciones como navegación básica, acceso a áreas seguras y recordar 
                    tu carrito de compras.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Ejemplos:</strong> Sesión de usuario, carrito de compras, preferencias de idioma
                  </div>
                </div>
                <div className="ml-4">
                  <div className="w-12 h-6 bg-green-500 rounded-full flex items-center justify-end px-1">
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Cookies de Análisis */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Cookies de Análisis
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Nos ayudan a entender cómo los visitantes interactúan con nuestro sitio web 
                    recopilando y reportando información de forma anónima. Esto nos permite 
                    mejorar la experiencia del usuario.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Servicios:</strong> Google Analytics, métricas de rendimiento
                    <br />
                    <strong>Datos recopilados:</strong> Páginas visitadas, tiempo en el sitio, fuente de tráfico
                  </div>
                </div>
                <div className="ml-4">
                  <button
                    onClick={toggleAnalytics}
                    className={`w-12 h-6 rounded-full flex items-center transition-colors ${
                      localConsent.analytics ? 'bg-primary justify-end' : 'bg-gray-300 justify-start'
                    }`}
                  >
                    <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                  </button>
                </div>
              </div>
            </div>

            {/* Cookies de Marketing */}
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-2">
                    <Target className="h-5 w-5 text-purple-600" />
                    Cookies de Marketing
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Se utilizan para mostrar anuncios relevantes y medir la efectividad de 
                    nuestras campañas publicitarias. También pueden ser utilizadas por 
                    terceros para crear un perfil de tus intereses.
                  </p>
                  <div className="text-xs text-gray-500">
                    <strong>Servicios:</strong> Google Ads, Facebook Pixel, remarketing
                    <br />
                    <strong>Propósito:</strong> Personalización de anuncios, seguimiento de conversiones
                  </div>
                </div>
                <div className="ml-4">
                  <button
                    onClick={toggleMarketing}
                    className={`w-12 h-6 rounded-full flex items-center transition-colors ${
                      localConsent.marketing ? 'bg-primary justify-end' : 'bg-gray-300 justify-start'
                    }`}
                  >
                    <div className="w-4 h-4 bg-white rounded-full mx-1"></div>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Información adicional */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">Información Adicional</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Las cookies se almacenan por un máximo de 12 meses</li>
              <li>• Puedes cambiar estas preferencias en cualquier momento</li>
              <li>• Eliminar cookies puede afectar la funcionalidad del sitio</li>
              <li>• Para más información, consulta nuestra <a href="/politica-privacidad" className="text-primary hover:underline">Política de Privacidad</a></li>
            </ul>
          </div>

          {/* Botones de acción */}
          <div className="flex flex-col sm:flex-row gap-3 mt-6 pt-6 border-t border-gray-200">
            <button
              onClick={handleRevokeAll}
              className="px-4 py-2 text-sm border border-red-300 text-red-700 hover:bg-red-50 rounded-md transition-colors"
            >
              Rechazar todas
            </button>
            <div className="flex-1"></div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={handleSave}
              className="px-6 py-2 text-sm bg-primary text-white hover:bg-primary-dark rounded-md transition-colors"
            >
              Guardar preferencias
            </button>
          </div>

          {/* Información de consentimiento actual */}
          {consent && (
            <div className="mt-4 text-xs text-gray-500 text-center">
              Última actualización: {new Date(consent.timestamp).toLocaleDateString('es-ES', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookiePreferences;
